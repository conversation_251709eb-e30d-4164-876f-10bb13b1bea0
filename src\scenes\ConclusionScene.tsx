import React from 'react';
import { AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import GradientText from '../components/reactbits/textanimations/GradientText';
import RotatingText from '../components/reactbits/textanimations/RotatingText';
import FuzzyText from '../components/reactbits/textanimations/FuzzyText';
import Aurora from '../components/reactbits/backgrounds/Aurora';

const ConclusionScene: React.FC<SceneProps> = ({ durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 问题动画
  const questionFadeIn = interpolate(currentFrame, [0, 30], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const questionScale = interpolate(currentFrame, [0, 30], [0.9, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 第一个答案动画
  const answer1FadeIn = interpolate(currentFrame, [90, 120], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 第二个答案动画
  const answer2FadeIn = interpolate(currentFrame, [180, 210], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 最终升华动画
  const finalMessageFadeIn = interpolate(currentFrame, [270, 300], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const finalMessageScale = interpolate(currentFrame, [270, 300], [0.8, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 背景渐变动画
  const bgShift = interpolate(currentFrame, [0, durationInFrames], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  return (
    <AbsoluteFill className="text-white relative overflow-hidden bg-black">
      {/* Aurora 背景 */}
      <div className="absolute inset-0 opacity-70">
        <Aurora
          colorStops={["#1e293b", "#4f46e5", "#9333ea", "#ec4899"]}
          amplitude={1.5}
          blend={0.8}
          speed={0.4}
        />
      </div>
      {/* 动态背景装饰 */}
      <div className="absolute inset-0 opacity-20">
        <div
          className="absolute w-96 h-96 bg-white rounded-full blur-3xl"
          style={{
            top: `${20 + Math.sin(currentFrame * 0.02) * 10}%`,
            left: `${15 + Math.cos(currentFrame * 0.015) * 8}%`,
            opacity: 0.1 + Math.sin(currentFrame * 0.03) * 0.05,
          }}
        ></div>
        <div
          className="absolute w-64 h-64 bg-blue-300 rounded-full blur-2xl"
          style={{
            bottom: `${25 + Math.cos(currentFrame * 0.025) * 12}%`,
            right: `${20 + Math.sin(currentFrame * 0.02) * 10}%`,
            opacity: 0.15 + Math.cos(currentFrame * 0.04) * 0.05,
          }}
        ></div>
        <div
          className="absolute w-48 h-48 bg-purple-300 rounded-full blur-2xl"
          style={{
            top: `${60 + Math.sin(currentFrame * 0.018) * 8}%`,
            right: `${30 + Math.cos(currentFrame * 0.022) * 6}%`,
            opacity: 0.12 + Math.sin(currentFrame * 0.035) * 0.04,
          }}
        ></div>
      </div>

      {/* 主内容区域 */}
      <div className="relative z-10 flex flex-col h-full justify-center items-center px-8">
        {/* 核心问题 */}
        <div
          className="text-center mb-16"
          style={{
            opacity: questionFadeIn,
            transform: `scale(${questionScale})`,
          }}
        >
          <GradientText
            className="text-4xl md:text-5xl font-bold mb-8"
            colors={["#fde047", "#fb923c", "#f97316"]}
            animationSpeed={4}
          >
            "早睡是伪命题吗？"
          </GradientText>
        </div>

        {/* 第一个答案 */}
        <div
          className="text-center mb-12 max-w-4xl"
          style={{ opacity: answer1FadeIn }}
        >
          <div className="bg-red-500/20 backdrop-blur-sm rounded-2xl p-8 border border-red-400/30 mb-8">
            <p className="text-xl md:text-2xl font-semibold text-red-200 mb-4">
              如果你把它当成万能钥匙
            </p>
            <p className="text-2xl md:text-3xl font-bold text-red-100">
              它确实是伪命题
            </p>
          </div>
        </div>

        {/* 第二个答案 */}
        <div
          className="text-center mb-16 max-w-4xl"
          style={{ opacity: answer2FadeIn }}
        >
          <div className="bg-green-500/20 backdrop-blur-sm rounded-2xl p-8 border border-green-400/30">
            <p className="text-xl md:text-2xl font-semibold text-green-200 mb-4">
              但如果理解为保持规律作息、尊重生物钟
            </p>
            <p className="text-2xl md:text-3xl font-bold text-green-100">
              那它就是改善生活的起点
            </p>
          </div>
        </div>

        {/* 最终升华信息 */}
        <div
          className="text-center max-w-5xl"
          style={{
            opacity: finalMessageFadeIn,
            transform: `scale(${finalMessageScale})`,
          }}
        >
          <div className="bg-gradient-to-r from-purple-500/30 to-pink-500/30 backdrop-blur-sm rounded-3xl p-12 border border-purple-400/40">
            <GradientText
              className="text-3xl md:text-4xl font-bold mb-6"
              colors={["#ffffff", "#c084fc", "#f9a8d4"]}
              animationSpeed={6}
            >
              健康的睡眠
            </GradientText>
            <div className="text-xl md:text-2xl text-gray-200 mb-4">
              <RotatingText
                texts={["不是简单的「早睡」二字", "而是规律、充足、高质量的休息"]}
                className="text-gray-200"
                rotationInterval={3000}
                staggerDuration={0.1}
              />
            </div>
            <div className="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mx-auto mb-6"></div>
            <div className="text-lg md:text-xl text-purple-200 italic">
              <FuzzyText
                fontSize="clamp(1rem, 2vw, 1.25rem)"
                color="#ddd6fe"
                enableHover={false}
                baseIntensity={0.08}
              >
                真正的改变，来自于对生活的全面理解和持续改善
              </FuzzyText>
            </div>
          </div>
        </div>
      </div>

      {/* 底部装饰 */}
      <div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        style={{ opacity: finalMessageFadeIn }}
      >
        <div className="flex space-x-2">
          <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
          <div className="w-3 h-3 bg-pink-400 rounded-full animate-pulse delay-200"></div>
          <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse delay-400"></div>
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default ConclusionScene;