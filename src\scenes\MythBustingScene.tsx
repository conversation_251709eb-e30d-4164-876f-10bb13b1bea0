import React from 'react';
import { AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import GradientText from '../components/reactbits/textanimations/GradientText';
import DecryptedText from '../components/reactbits/textanimations/DecryptedText';
import PrismaticBurst from '../components/reactbits/backgrounds/PrismaticBurst';

const MythBustingScene: React.FC<SceneProps> = ({ durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 标题动画
  const titleFadeIn = interpolate(currentFrame, [0, 30], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 引言动画
  const introFadeIn = interpolate(currentFrame, [60, 90], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 三个被忽略因素的依次出现
  const factor1FadeIn = interpolate(currentFrame, [120, 150], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const factor2FadeIn = interpolate(currentFrame, [180, 210], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const factor3FadeIn = interpolate(currentFrame, [240, 270], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 例子动画
  const exampleFadeIn = interpolate(currentFrame, [300, 330], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 结论动画
  const conclusionFadeIn = interpolate(currentFrame, [390, 420], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const conclusionScale = interpolate(currentFrame, [390, 420], [0.9, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  return (
    <AbsoluteFill className="bg-gradient-to-br from-red-900 via-orange-900 to-yellow-900 text-white relative overflow-hidden">
      {/* PrismaticBurst 背景 */}
      <div className="absolute inset-0 opacity-50">
        <PrismaticBurst
          intensity={0.8}
          speed={0.6}
          animationType="rotate"
          colors={["#ef4444", "#f97316", "#eab308"]}
          distort={0.3}
          rayCount={12}
          mixBlendMode="multiply"
        />
      </div>

      {/* 主内容区域 */}
      <div className="relative z-10 flex flex-col h-full">
        {/* 标题区域 */}
        <div
          className="text-center pt-16 pb-8"
          style={{ opacity: titleFadeIn }}
        >
          <GradientText
            className="text-5xl md:text-6xl font-bold mb-4"
            colors={["#fca5a5", "#fdba74", "#fde047"]}
            animationSpeed={4}
          >
            第三部分
          </GradientText>
          <div className="text-3xl md:text-4xl font-semibold text-red-100">
            <DecryptedText
              text="为什么说「早睡改命」是伪命题"
              className="text-red-100"
              speed={80}
              maxIterations={8}
              sequential={true}
              animateOn="view"
            />
          </div>
        </div>

        {/* 引言 */}
        <div
          className="text-center mb-12 px-8"
          style={{ opacity: introFadeIn }}
        >
          <p className="text-xl md:text-2xl text-yellow-300 font-medium">
            把"早睡"当成改变命运的钥匙，就忽略了其他重要因素：
          </p>
        </div>

        {/* 三个被忽略的因素 */}
        <div className="flex-1 flex flex-col justify-center px-16 space-y-8">
          {/* 因素1：睡眠质量 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-red-400/30"
            style={{ opacity: factor1FadeIn }}
          >
            <div className="flex items-start">
              <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mr-6 mt-1">
                <span className="text-2xl">😴</span>
              </div>
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-red-200 mb-3">
                  睡眠质量
                </h3>
                <p className="text-lg md:text-xl text-gray-200">
                  是否深度睡眠足够
                </p>
              </div>
            </div>
          </div>

          {/* 因素2：睡眠总时长 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-orange-400/30"
            style={{ opacity: factor2FadeIn }}
          >
            <div className="flex items-start">
              <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mr-6 mt-1">
                <span className="text-2xl">⏰</span>
              </div>
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-orange-200 mb-3">
                  睡眠总时长
                </h3>
                <p className="text-lg md:text-xl text-gray-200">
                  补觉不能完全抵消熬夜
                </p>
              </div>
            </div>
          </div>

          {/* 因素3：生活方式 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-yellow-400/30"
            style={{ opacity: factor3FadeIn }}
          >
            <div className="flex items-start">
              <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mr-6 mt-1">
                <span className="text-2xl">🏃‍♂️</span>
              </div>
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-yellow-200 mb-3">
                  生活方式
                </h3>
                <p className="text-lg md:text-xl text-gray-200">
                  饮食、运动、压力管理同样关键
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 例子说明 */}
        <div
          className="text-center mb-8 px-8"
          style={{ opacity: exampleFadeIn }}
        >
          <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-6 border border-purple-400/30 max-w-4xl mx-auto">
            <p className="text-lg md:text-xl text-purple-200">
              如果你早睡，但每天都被打断、睡眠浅，依旧会疲惫
            </p>
          </div>
        </div>

        {/* 结论 */}
        <div
          className="text-center pb-16 px-8"
          style={{
            opacity: conclusionFadeIn,
            transform: `scale(${conclusionScale})`,
          }}
        >
          <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-8 border border-red-400/30 max-w-4xl mx-auto">
            <p className="text-2xl md:text-3xl font-bold text-red-200 mb-4">
              所以，早睡不是"改命"
            </p>
            <p className="text-xl md:text-2xl text-orange-200">
              而是健康生活的一部分
            </p>
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default MythBustingScene;