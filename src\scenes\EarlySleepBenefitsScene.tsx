import React from 'react';
import { AbsoluteFill, interpolate, useCurrentFrame } from 'remotion';
import { SceneProps } from '../types';
import GradientText from '../components/reactbits/textanimations/GradientText';
import ShinyText from '../components/reactbits/textanimations/ShinyText';
import FuzzyText from '../components/reactbits/textanimations/FuzzyText';
import Orb from '../components/reactbits/backgrounds/Orb';

const EarlySleepBenefitsScene: React.FC<SceneProps> = ({ durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 标题动画
  const titleFadeIn = interpolate(currentFrame, [0, 30], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 引言动画
  const introFadeIn = interpolate(currentFrame, [60, 90], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 三个益处的依次出现
  const benefit1FadeIn = interpolate(currentFrame, [120, 150], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const benefit2FadeIn = interpolate(currentFrame, [180, 210], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const benefit3FadeIn = interpolate(currentFrame, [240, 270], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  // 结论动画
  const conclusionFadeIn = interpolate(currentFrame, [330, 360], [0, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  const conclusionScale = interpolate(currentFrame, [330, 360], [0.9, 1], {
    extrapolateLeft: 'clamp',
    extrapolateRight: 'clamp',
  });

  return (
    <AbsoluteFill className="bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 text-white relative overflow-hidden">
      {/* Orb 背景 */}
      <div className="absolute inset-0 opacity-40">
        <Orb
          hue={120} // 绿色调
          hoverIntensity={0.3}
          rotateOnHover={false}
          forceHoverState={true}
        />
      </div>

      {/* 额外的装饰光球 */}
      <div className="absolute top-20 right-20 w-32 h-32 opacity-30">
        <Orb
          hue={180} // 青色调
          hoverIntensity={0.2}
          rotateOnHover={false}
          forceHoverState={false}
        />
      </div>

      {/* 主内容区域 */}
      <div className="relative z-10 flex flex-col h-full">
        {/* 标题区域 */}
        <div
          className="text-center pt-16 pb-8"
          style={{ opacity: titleFadeIn }}
        >
          <GradientText
            className="text-5xl md:text-6xl font-bold mb-4"
            colors={["#34d399", "#06b6d4", "#3b82f6"]}
            animationSpeed={5}
          >
            第二部分
          </GradientText>
          <div className="text-3xl md:text-4xl font-semibold">
            <FuzzyText
              fontSize="clamp(1.5rem, 4vw, 2.5rem)"
              color="#d1fae5"
              enableHover={false}
              baseIntensity={0.1}
            >
              早睡的确有益处，但并非唯一关键
            </FuzzyText>
          </div>
        </div>

        {/* 引言 */}
        <div
          className="text-center mb-12 px-8"
          style={{ opacity: introFadeIn }}
        >
          <ShinyText
            text="早睡确实对身体有好处，尤其是和「昼夜节律」保持一致的时候"
            className="text-xl md:text-2xl text-yellow-300 font-medium"
            speed={3}
          />
        </div>

        {/* 三个益处 */}
        <div className="flex-1 flex flex-col justify-center px-16 space-y-10">
          {/* 益处1：褪黑素分泌 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-green-400/30"
            style={{ opacity: benefit1FadeIn }}
          >
            <div className="flex items-start">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-6 mt-1">
                <span className="text-2xl">🌙</span>
              </div>
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-green-200 mb-3">
                  褪黑素分泌
                </h3>
                <p className="text-lg md:text-xl text-gray-200">
                  通常在晚上 9–11 点开始增加，过晚入睡可能打乱节律
                </p>
              </div>
            </div>
          </div>

          {/* 益处2：学习和记忆 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-teal-400/30"
            style={{ opacity: benefit2FadeIn }}
          >
            <div className="flex items-start">
              <div className="w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center mr-6 mt-1">
                <span className="text-2xl">🧠</span>
              </div>
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-teal-200 mb-3">
                  学习和记忆
                </h3>
                <p className="text-lg md:text-xl text-gray-200">
                  研究发现，较早入睡的人，第二天的专注力和工作效率更高
                </p>
              </div>
            </div>
          </div>

          {/* 益处3：心理健康 */}
          <div
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-blue-400/30"
            style={{ opacity: benefit3FadeIn }}
          >
            <div className="flex items-start">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-6 mt-1">
                <span className="text-2xl">💚</span>
              </div>
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-blue-200 mb-3">
                  心理健康
                </h3>
                <p className="text-lg md:text-xl text-gray-200">
                  长期熬夜会增加焦虑和抑郁风险
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 结论 */}
        <div
          className="text-center pb-16 px-8"
          style={{
            opacity: conclusionFadeIn,
            transform: `scale(${conclusionScale})`,
          }}
        >
          <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-8 border border-yellow-400/30 max-w-4xl mx-auto">
            <p className="text-2xl md:text-3xl font-bold text-yellow-200 mb-4">
              所以，早睡确实对身体有好处
            </p>
            <p className="text-xl md:text-2xl text-orange-200">
              尤其是和"昼夜节律"保持一致的时候
            </p>
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default EarlySleepBenefitsScene;