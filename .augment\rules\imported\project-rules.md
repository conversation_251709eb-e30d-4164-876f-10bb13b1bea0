---
type: "always_apply"
description: "Example description"
---
# Project Rules for Remotion + React-bits

## Project Overview
This is a Remotion video project using React 19, TypeScript, Tailwind CSS v4, and react-bits component library for enhanced UI animations and effects.

## Technology Stack
- **Framework**: Remotion 4.0.340 for video generation
- **Frontend**: React 19 with TypeScript 5.8.2
- **Styling**: Tailwind CSS v4 with @remotion/tailwind-v4
- **Components**: react-bits for advanced animations and UI components
- **Linting**: ESLint with @remotion/eslint-config-flat
- **Formatting**: Prettier 3.6.0

## Code Style Guidelines

### TypeScript/React
- Use TypeScript for all new files (.tsx for React components, .ts for utilities)
- Prefer functional components with hooks over class components
- Use proper TypeScript types and interfaces, avoid `any`
- Export components as default exports for main components
- Use named exports for utilities and helper functions

### Component Structure
```typescript
// Preferred component structure
import React from 'react';
import { AbsoluteFill } from 'remotion';

interface ComponentProps {
  // Define props with proper types
}

export const ComponentName: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  return (
    <AbsoluteFill className="...">
      {/* Component content */}
    </AbsoluteFill>
  );
};

export default ComponentName;
```

### React-bits Integration
- Import react-bits components from the library when available
- Use react-bits for animations, text effects, backgrounds, and interactive components
- Prefer react-bits components over custom implementations for common UI patterns
- Categories available:
  - **TextAnimations**: For text effects and typography animations
  - **Animations**: For interactive animations and cursor effects
  - **Components**: For complete UI components with advanced interactions
  - **Backgrounds**: For immersive background effects

### Remotion Specific
- Use `AbsoluteFill` for full-screen layouts
- Use `useCurrentFrame()` and `useVideoConfig()` for frame-based animations
- Prefer `interpolate()` for smooth value transitions
- Use `Sequence` for timing different parts of the composition
- Keep compositions in separate files and register them in `src/Root.tsx`

### Styling with Tailwind CSS
- Use Tailwind CSS v4 classes for styling
- Prefer utility classes over custom CSS when possible
- Use responsive design patterns with Tailwind breakpoints
- Leverage Tailwind's animation utilities alongside react-bits components
- Custom CSS should be minimal and placed in `src/index.css`

### File Organization
```
src/
├── components/          # Reusable React components
├── compositions/        # Remotion composition files
├── utils/              # Utility functions and helpers
├── types/              # TypeScript type definitions
├── assets/             # Static assets (images, fonts, etc.)
├── Composition.tsx     # Main composition
├── Root.tsx           # Remotion root with composition registry
└── index.ts           # Entry point
```

### Naming Conventions
- **Files**: PascalCase for components (`MyComponent.tsx`), camelCase for utilities (`myUtil.ts`)
- **Components**: PascalCase (`MyComponent`)
- **Variables/Functions**: camelCase (`myVariable`, `myFunction`)
- **Constants**: UPPER_SNAKE_CASE (`MY_CONSTANT`)
- **Types/Interfaces**: PascalCase with descriptive names (`UserData`, `ComponentProps`)

### Performance Guidelines
- Use React.memo() for components that don't need frequent re-renders
- Optimize heavy computations with useMemo() and useCallback()
- Be mindful of frame rate - avoid expensive operations in render loops
- Use react-bits components which are optimized for performance
- Lazy load heavy assets when possible

### Error Handling
- Use proper error boundaries for React components
- Handle async operations with proper try-catch blocks
- Provide meaningful error messages for debugging
- Use TypeScript strict mode to catch errors at compile time

### Testing Considerations
- Write unit tests for utility functions
- Test component rendering with different props
- Test Remotion compositions at different frame positions
- Ensure react-bits components integrate properly with Remotion

### Dependencies Management
- Keep dependencies up to date, especially Remotion and react-bits
- Use exact versions for critical dependencies
- Prefer peer dependencies when possible to avoid version conflicts
- Document any specific version requirements

### Git Workflow
- Use conventional commit messages
- Keep commits focused and atomic
- Use meaningful branch names
- Include tests with new features

## 🎬 视频排版统一标准

### 1. 屏幕布局

#### 上方：标题区
- 使用大号字体（48px~64px）
- 加粗，居中，颜色建议用高对比度（白字 + 半透明黑底 或 黑字 + 浅底）
- 可加轻微的入场动画（淡入/滑入）

#### 中间：主体区
- 放主要内容（人物/画面/图示/动画）
- 保持留白，不要被文字遮挡
- 主体画面周围预留安全区（10% 边距），避免被平台裁切

#### 下方：字幕/辅助说明区
- 小字号（28px~36px），居中或靠下居中
- 建议使用浅色字体 + 深色描边（避免跟背景混淆）
- 每条字幕控制在不超过两行，每行15字左右

### 2. 文字规范

#### 字体选择
- **中文**: 思源黑体 / HarmonyOS Sans / 苹方（简洁现代感）
- **英文**: Roboto / Inter / Helvetica Neue

#### 字号层级
- **标题**: 48~64px
- **小标题/重点词**: 36~44px
- **正文/字幕**: 28~36px

#### 字体粗细
- **标题/重点**: Bold
- **正文/说明**: Regular/Medium

#### 配色建议（保持统一色彩系统）
- **背景深** → 白字 + 半透明深色遮罩
- **背景浅** → 黑字 + 半透明浅色遮罩
- **强调关键词** → 主色调（如蓝/橙/红）

### 3. 主体展示标准

#### 人物出镜
- 居中或三分构图（左右偏移）
- 背景保持简洁，避免杂乱干扰

#### 素材/插画/图表
- 尽量使用同一套风格（例如：线性图标 / 扁平化插画 / 真实素材）
- 数据图表保持统一配色（同一主题色系 + 灰度辅助）
- 图表进入时可用「缩放淡入」动画，突出重点数据时用高亮

### 4. 动效与转场

#### 文字入场
- 淡入、上滑、打字机效果（保持 0.3~0.5s）

#### 转场
- 溶解 / 推进 / 蒙版滑动（不要过度花哨）

#### 强调重点
- 放大、加描边、变色（短暂出现 1~2 秒）

### 5. 实现建议

#### Remotion + Tailwind CSS 实现

#### 动画时间常量
```typescript
// 统一的动画时间配置
export const ANIMATION_DURATIONS = {
  TEXT_FADE_IN: 30,      // 0.5s at 60fps
  TEXT_SLIDE_UP: 24,     // 0.4s at 60fps
  TRANSITION: 36,        // 0.6s at 60fps
  EMPHASIS: 60,          // 1s at 60fps
} as const;

// 统一的缓动函数
export const EASING = {
  EASE_OUT: [0.25, 0.46, 0.45, 0.94],
  EASE_IN_OUT: [0.42, 0, 0.58, 1],
  BOUNCE: [0.68, -0.55, 0.265, 1.55],
} as const;
```

## Best Practices
1. **Performance**: Always consider the impact on render performance
2. **Accessibility**: Ensure animations don't cause motion sickness
3. **Responsiveness**: Test on different screen sizes
4. **Type Safety**: Use TypeScript features to prevent runtime errors
5. **Code Reuse**: Create reusable components and utilities
6. **Documentation**: Comment complex logic and animation sequences
7. **react-bits Integration**: Leverage the library's optimized components instead of reinventing
8. **Remotion Optimization**: Use Remotion's built-in optimization features
9. **视频排版一致性**: 严格遵循排版标准，确保所有视频内容的视觉统一性
10. **动画流畅性**: 使用统一的动画时长和缓动函数，保持视觉体验的连贯性
